#include <iostream>
#include <chrono>
#include <vector>
#include <memory>
#include <atomic>
#include <thread>
#include "raft-kv/rpc/mprpcchannel.h"
#include "raft-kv/rpc/mprpccontroller.h"
#include "raft-kv/fiber/iomanager.h"
#include "raft-kv/fiber/monsoon.h"
#include "raftRPC.pb.h"

using namespace std;
using namespace monsoon;

/**
 * @brief 异步RPC性能测试
 * 
 * 这个测试用例演示了异步RPC相比同步RPC的性能优势：
 * 1. 同步RPC：串行发送请求，每个请求都要等待响应
 * 2. 异步RPC：并行发送请求，可以同时等待多个响应
 */

class AsyncRpcTest {
private:
    IOManager::ptr ioManager_;
    std::vector<std::unique_ptr<MprpcChannel>> channels_;
    std::atomic<int> completedRequests_{0};
    std::atomic<int> successfulRequests_{0};
    
public:
    AsyncRpcTest() {
        // 创建IOManager，启用异步模式
        ioManager_ = std::make_shared<IOManager>(4, true, "AsyncRpcTest");
        
        // 创建多个RPC通道连接到不同的服务器
        // 注意：这里假设有多个Raft节点在运行
        std::vector<std::pair<std::string, int>> servers = {
            {"127.0.0.1", 8000},
            {"127.0.0.1", 8001},
            {"127.0.0.1", 8002}
        };
        
        for (auto& server : servers) {
            channels_.emplace_back(
                std::make_unique<MprpcChannel>(server.first, server.second, false)
            );
        }
    }
    
    ~AsyncRpcTest() {
        if (ioManager_) {
            ioManager_->stop();
        }
    }
    
    /**
     * @brief 测试同步RPC性能
     */
    void testSyncRpc(int requestCount) {
        cout << "\n=== 同步RPC测试 ===" << endl;
        cout << "发送 " << requestCount << " 个请求..." << endl;
        
        auto start = chrono::high_resolution_clock::now();
        
        for (int i = 0; i < requestCount; ++i) {
            // 轮询选择服务器
            auto& channel = channels_[i % channels_.size()];
            
            // 创建请求
            raftRpcProctoc::RequestVoteArgs args;
            args.set_term(1);
            args.set_candidateid(0);
            args.set_lastlogindex(0);
            args.set_lastlogterm(0);
            
            raftRpcProctoc::RequestVoteReply reply;
            MprpcController controller;
            
            // 获取方法描述符
            const auto* service_desc = raftRpcProctoc::raftRpc::descriptor();
            const auto* method_desc = service_desc->FindMethodByName("RequestVote");
            
            // 同步调用
            channel->CallMethod(method_desc, &controller, &args, &reply, nullptr);
            
            if (!controller.Failed()) {
                successfulRequests_++;
            }
            completedRequests_++;
        }
        
        auto end = chrono::high_resolution_clock::now();
        auto duration = chrono::duration_cast<chrono::milliseconds>(end - start);
        
        cout << "同步RPC完成: " << completedRequests_ << " 个请求" << endl;
        cout << "成功: " << successfulRequests_ << " 个" << endl;
        cout << "耗时: " << duration.count() << " ms" << endl;
        cout << "平均延迟: " << (double)duration.count() / requestCount << " ms/请求" << endl;
        
        // 重置计数器
        completedRequests_ = 0;
        successfulRequests_ = 0;
    }
    
    /**
     * @brief 测试异步RPC性能
     */
    void testAsyncRpc(int requestCount) {
        cout << "\n=== 异步RPC测试 ===" << endl;
        cout << "发送 " << requestCount << " 个请求..." << endl;
        
        auto start = chrono::high_resolution_clock::now();
        
        // 在IOManager中执行异步测试
        ioManager_->scheduler([this, requestCount, start]() {
            this->runAsyncTest(requestCount, start);
        });
        
        // 等待所有请求完成
        while (completedRequests_ < requestCount) {
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
        
        auto end = chrono::high_resolution_clock::now();
        auto duration = chrono::duration_cast<chrono::milliseconds>(end - start);
        
        cout << "异步RPC完成: " << completedRequests_ << " 个请求" << endl;
        cout << "成功: " << successfulRequests_ << " 个" << endl;
        cout << "耗时: " << duration.count() << " ms" << endl;
        cout << "平均延迟: " << (double)duration.count() / requestCount << " ms/请求" << endl;
        
        // 重置计数器
        completedRequests_ = 0;
        successfulRequests_ = 0;
    }
    
private:
    void runAsyncTest(int requestCount, chrono::high_resolution_clock::time_point start) {
        for (int i = 0; i < requestCount; ++i) {
            // 轮询选择服务器
            auto& channel = channels_[i % channels_.size()];
            
            // 创建请求
            auto args = std::make_shared<raftRpcProctoc::RequestVoteArgs>();
            args->set_term(1);
            args->set_candidateid(0);
            args->set_lastlogindex(0);
            args->set_lastlogterm(0);
            
            auto reply = std::make_shared<raftRpcProctoc::RequestVoteReply>();
            auto controller = std::make_shared<MprpcController>();
            
            // 获取方法描述符
            const auto* service_desc = raftRpcProctoc::raftRpc::descriptor();
            const auto* method_desc = service_desc->FindMethodByName("RequestVote");
            
            // 异步调用
            channel->CallMethodAsync(method_desc, controller.get(), args.get(), reply.get(),
                [this, controller, reply](bool success, google::protobuf::Message* msg) {
                    if (success) {
                        successfulRequests_++;
                    }
                    completedRequests_++;
                });
        }
    }
    
public:
    /**
     * @brief 运行完整的性能对比测试
     */
    void runPerformanceComparison() {
        cout << "异步RPC性能测试开始..." << endl;
        cout << "注意：此测试需要Raft服务器在运行" << endl;
        
        const int requestCount = 100;
        
        // 测试同步RPC
        testSyncRpc(requestCount);
        
        // 等待一段时间
        std::this_thread::sleep_for(std::chrono::seconds(1));
        
        // 测试异步RPC
        testAsyncRpc(requestCount);
        
        cout << "\n=== 性能对比总结 ===" << endl;
        cout << "异步RPC的主要优势：" << endl;
        cout << "1. 非阻塞：发送请求后立即返回，不等待响应" << endl;
        cout << "2. 并发：可以同时发送多个请求" << endl;
        cout << "3. 高效：充分利用网络带宽和系统资源" << endl;
        cout << "4. 可扩展：适合高并发场景" << endl;
    }
};

int main() {
    try {
        AsyncRpcTest test;
        test.runPerformanceComparison();
    } catch (const std::exception& e) {
        cerr << "测试异常: " << e.what() << endl;
        return 1;
    }
    
    return 0;
}
