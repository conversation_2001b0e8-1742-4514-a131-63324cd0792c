#include <iostream>
#include <memory>
#include <future>
#include <vector>
#include "raft-kv/rpc/mprpcchannel.h"
#include "raft-kv/rpc/mprpccontroller.h"
#include "raft-kv/fiber/iomanager.h"
#include "raftRPC.pb.h"

using namespace std;
using namespace monsoon;

/**
 * @brief 异步RPC使用示例
 * 
 * 这个示例展示了如何使用新的异步RPC接口：
 * 1. 回调风格的异步调用
 * 2. Future风格的异步调用
 * 3. 批量异步调用的性能优势
 */

class AsyncRpcDemo {
private:
    IOManager::ptr ioManager_;
    std::unique_ptr<MprpcChannel> channel_;
    
public:
    AsyncRpcDemo() {
        // 创建IOManager，启用异步模式
        ioManager_ = std::make_shared<IOManager>(2, true, "AsyncRpcDemo");
        
        // 创建RPC通道
        channel_ = std::make_unique<MprpcChannel>("127.0.0.1", 8000, false);
    }
    
    ~AsyncRpcDemo() {
        if (ioManager_) {
            ioManager_->stop();
        }
    }
    
    /**
     * @brief 演示回调风格的异步RPC调用
     */
    void demoCallbackStyle() {
        cout << "\n=== 回调风格异步RPC演示 ===" << endl;
        
        ioManager_->scheduler([this]() {
            // 创建请求
            raftRpcProctoc::RequestVoteArgs args;
            args.set_term(1);
            args.set_candidateid(0);
            args.set_lastlogindex(0);
            args.set_lastlogterm(0);
            
            raftRpcProctoc::RequestVoteReply reply;
            MprpcController controller;
            
            // 获取方法描述符
            const auto* service_desc = raftRpcProctoc::raftRpc::descriptor();
            const auto* method_desc = service_desc->FindMethodByName("RequestVote");
            
            cout << "发送异步RequestVote请求..." << endl;
            
            // 异步调用（回调风格）
            channel_->CallMethodAsync(method_desc, &controller, &args, &reply,
                [](bool success, google::protobuf::Message* msg) {
                    if (success) {
                        auto* vote_reply = dynamic_cast<raftRpcProctoc::RequestVoteReply*>(msg);
                        if (vote_reply) {
                            cout << "RequestVote成功! Term: " << vote_reply->term() 
                                 << ", VoteGranted: " << vote_reply->votegranted() << endl;
                        }
                    } else {
                        cout << "RequestVote失败!" << endl;
                    }
                });
            
            cout << "异步请求已发送，继续执行其他任务..." << endl;
        });
    }
    
    /**
     * @brief 演示Future风格的异步RPC调用
     */
    void demoFutureStyle() {
        cout << "\n=== Future风格异步RPC演示 ===" << endl;
        
        ioManager_->scheduler([this]() {
            // 创建请求
            raftRpcProctoc::AppendEntriesArgs args;
            args.set_term(1);
            args.set_leaderid(0);
            args.set_prevlogindex(0);
            args.set_prevlogterm(0);
            args.set_leadercommit(0);
            
            raftRpcProctoc::AppendEntriesReply reply;
            MprpcController controller;
            
            // 获取方法描述符
            const auto* service_desc = raftRpcProctoc::raftRpc::descriptor();
            const auto* method_desc = service_desc->FindMethodByName("AppendEntries");
            
            cout << "发送异步AppendEntries请求..." << endl;
            
            // 异步调用（Future风格）
            auto future = channel_->CallMethodAsync(method_desc, &controller, &args, &reply);
            
            cout << "异步请求已发送，可以做其他事情..." << endl;
            
            // 等待结果
            cout << "等待响应..." << endl;
            auto result = future.get();
            
            if (result) {
                auto* append_reply = dynamic_cast<raftRpcProctoc::AppendEntriesReply*>(result.get());
                if (append_reply) {
                    cout << "AppendEntries成功! Term: " << append_reply->term() 
                         << ", Success: " << append_reply->success() << endl;
                }
            } else {
                cout << "AppendEntries失败!" << endl;
            }
        });
    }
    
    /**
     * @brief 演示批量异步调用的性能优势
     */
    void demoBatchAsync() {
        cout << "\n=== 批量异步RPC演示 ===" << endl;
        
        ioManager_->scheduler([this]() {
            const int batchSize = 5;
            vector<future<unique_ptr<google::protobuf::Message>>> futures;
            
            cout << "同时发送 " << batchSize << " 个异步请求..." << endl;
            
            // 同时发送多个异步请求
            for (int i = 0; i < batchSize; ++i) {
                raftRpcProctoc::RequestVoteArgs args;
                args.set_term(i + 1);
                args.set_candidateid(0);
                args.set_lastlogindex(i);
                args.set_lastlogterm(i);
                
                raftRpcProctoc::RequestVoteReply reply;
                MprpcController controller;
                
                const auto* service_desc = raftRpcProctoc::raftRpc::descriptor();
                const auto* method_desc = service_desc->FindMethodByName("RequestVote");
                
                // 发送异步请求并保存future
                futures.push_back(
                    channel_->CallMethodAsync(method_desc, &controller, &args, &reply)
                );
                
                cout << "请求 " << (i + 1) << " 已发送" << endl;
            }
            
            cout << "所有请求已发送，等待响应..." << endl;
            
            // 等待所有响应
            for (int i = 0; i < batchSize; ++i) {
                auto result = futures[i].get();
                if (result) {
                    auto* vote_reply = dynamic_cast<raftRpcProctoc::RequestVoteReply*>(result.get());
                    if (vote_reply) {
                        cout << "请求 " << (i + 1) << " 响应: Term=" << vote_reply->term() 
                             << ", VoteGranted=" << vote_reply->votegranted() << endl;
                    }
                } else {
                    cout << "请求 " << (i + 1) << " 失败" << endl;
                }
            }
            
            cout << "所有批量请求处理完成!" << endl;
        });
    }
    
    /**
     * @brief 运行所有演示
     */
    void runAllDemos() {
        cout << "异步RPC使用演示开始..." << endl;
        cout << "注意：此演示需要Raft服务器在127.0.0.1:8000运行" << endl;
        
        // 演示回调风格
        demoCallbackStyle();
        std::this_thread::sleep_for(std::chrono::seconds(2));
        
        // 演示Future风格
        demoFutureStyle();
        std::this_thread::sleep_for(std::chrono::seconds(2));
        
        // 演示批量异步
        demoBatchAsync();
        std::this_thread::sleep_for(std::chrono::seconds(3));
        
        cout << "\n=== 演示总结 ===" << endl;
        cout << "异步RPC的优势：" << endl;
        cout << "1. 非阻塞：调用后立即返回，不等待响应" << endl;
        cout << "2. 高并发：可以同时处理多个请求" << endl;
        cout << "3. 灵活性：支持回调和Future两种风格" << endl;
        cout << "4. 性能：充分利用网络和CPU资源" << endl;
        cout << "\n在Raft算法中的应用场景：" << endl;
        cout << "1. Leader向所有Follower并行发送心跳" << endl;
        cout << "2. Candidate向所有节点并行请求投票" << endl;
        cout << "3. Leader向Follower并行复制日志" << endl;
        cout << "4. 快照安装的并行传输" << endl;
    }
};

int main() {
    try {
        AsyncRpcDemo demo;
        demo.runAllDemos();
    } catch (const std::exception& e) {
        cerr << "演示异常: " << e.what() << endl;
        return 1;
    }
    
    return 0;
}
