# RPC 框架异步化改进

## 概述

本文档描述了对 RPC 框架的异步化改进，实现了真正的非阻塞异步 RPC 客户端，与 IOManager 深度集成，显著提升了 Raft 算法的并发性能。

## 改进背景

### 原有问题

1. **同步阻塞**：`MprpcChannel::CallMethod` 是同步阻塞的，会一直 `recv` 直到收到响应
2. **协程阻塞**：虽然在协程中调用不会阻塞物理线程，但无法充分发挥并发优势
3. **性能瓶颈**：Raft 心跳和日志复制需要串行等待每个节点的响应

### 改进目标

1. 实现真正的非阻塞异步 RPC 调用
2. 与 IOManager 深度集成，利用事件驱动机制
3. 支持并发发送多个请求，在事件循环中等待所有结果
4. 提供回调和 Future 两种异步编程风格

## 技术方案

### 1. 异步请求上下文

```cpp
struct AsyncRpcContext {
  uint64_t requestId;                                                    // 请求ID
  std::function<void(bool, google::protobuf::Message*)> callback;       // 回调函数
  std::shared_ptr<std::promise<std::unique_ptr<google::protobuf::Message>>> promise; // Promise对象
  std::unique_ptr<google::protobuf::Message> response;                  // 响应消息
  std::chrono::steady_clock::time_point startTime;                     // 请求开始时间
};
```

### 2. 异步接口设计

#### 回调风格
```cpp
void CallMethodAsync(const google::protobuf::MethodDescriptor *method,
                     google::protobuf::RpcController *controller,
                     const google::protobuf::Message *request,
                     google::protobuf::Message *response,
                     std::function<void(bool, google::protobuf::Message*)> callback);
```

#### Future风格
```cpp
std::future<std::unique_ptr<google::protobuf::Message>> CallMethodAsync(
    const google::protobuf::MethodDescriptor *method,
    google::protobuf::RpcController *controller,
    const google::protobuf::Message *request,
    const google::protobuf::Message *response);
```

### 3. 协议扩展

扩展了 RPC 头部协议，添加了 `request_id` 字段用于异步请求-响应匹配：

```protobuf
message RpcHeader {
    bytes service_name = 1;  // 服务名称
    bytes method_name = 2;   // 方法名称
    uint32 args_size = 3;    // 参数大小
    uint64 request_id = 4;   // 请求ID（新增）
}
```

### 4. IOManager 集成

- **发送阶段**：使用非阻塞 `send()` 发送请求
- **接收阶段**：将 socket 的 READ 事件注册到 IOManager
- **事件处理**：当 socket 可读时，IOManager 调用响应处理回调
- **并发处理**：可以同时注册多个 socket 的 READ 事件

## 实现细节

### 1. 异步发送流程

```cpp
bool MprpcChannel::sendAsyncRequest(...) {
    // 1. 检查连接状态
    // 2. 序列化请求数据
    // 3. 非阻塞发送请求
    ssize_t sent = send(m_clientFd, data, size, MSG_DONTWAIT);
    
    // 4. 注册READ事件等待响应
    m_ioManager->addEvent(m_clientFd, monsoon::READ, 
                         [this]() { this->handleAsyncResponse(); });
    return true;
}
```

### 2. 异步响应处理

```cpp
void MprpcChannel::handleAsyncResponse() {
    // 1. 非阻塞接收响应数据
    recv(m_clientFd, buffer, size, MSG_DONTWAIT);
    
    // 2. 查找对应的请求上下文
    auto context = findPendingRequest(requestId);
    
    // 3. 反序列化响应数据
    context->response->ParseFromArray(buffer, size);
    
    // 4. 触发回调或设置Promise
    if (context->callback) {
        context->callback(true, context->response.get());
    }
    if (context->promise) {
        context->promise->set_value(std::move(context->response));
    }
}
```

### 3. RaftRpcUtil 更新

更新了 `RaftRpcUtil` 以使用真正的异步接口：

```cpp
void RaftRpcUtil::AppendEntriesAsync(...) {
    MprpcChannel* channel = dynamic_cast<MprpcChannel*>(stub_->channel());
    if (channel) {
        // 使用真正的异步接口
        channel->CallMethodAsync(method_desc, &controller, args, response, callback);
    } else {
        // 回退到协程模式
        ioManager->scheduler([this, args, response, callback]() {
            bool success = this->AppendEntries(args, response);
            callback(success, response);
        });
    }
}
```

## 性能优势

### 1. 并发优势

**同步模式**：
```
请求1 -> 等待响应1 -> 请求2 -> 等待响应2 -> 请求3 -> 等待响应3
总时间 = 3 * 网络延迟
```

**异步模式**：
```
请求1 -> 请求2 -> 请求3 -> 等待所有响应
总时间 = 1 * 网络延迟
```

### 2. 资源利用

- **CPU**：发送请求后立即释放CPU，可以处理其他任务
- **网络**：充分利用网络带宽，并行传输多个请求
- **内存**：请求上下文按需分配，响应后立即释放

### 3. Raft 算法优化

- **心跳发送**：Leader 可以并行向所有 Follower 发送心跳
- **投票请求**：Candidate 可以并行向所有节点请求投票
- **日志复制**：Leader 可以并行向 Follower 复制日志
- **快照安装**：并行传输快照数据

## 使用示例

### 1. 回调风格

```cpp
channel->CallMethodAsync(method, &controller, &request, &response,
    [](bool success, google::protobuf::Message* msg) {
        if (success) {
            // 处理成功响应
            auto* reply = dynamic_cast<AppendEntriesReply*>(msg);
            cout << "Success: " << reply->success() << endl;
        } else {
            // 处理失败
            cout << "RPC failed" << endl;
        }
    });
```

### 2. Future风格

```cpp
auto future = channel->CallMethodAsync(method, &controller, &request, &response);

// 可以做其他事情...

auto result = future.get();  // 等待结果
if (result) {
    auto* reply = dynamic_cast<AppendEntriesReply*>(result.get());
    cout << "Success: " << reply->success() << endl;
}
```

### 3. 批量异步调用

```cpp
vector<future<unique_ptr<Message>>> futures;

// 并行发送多个请求
for (int i = 0; i < nodeCount; ++i) {
    futures.push_back(
        channels[i]->CallMethodAsync(method, &controller, &request, &response)
    );
}

// 等待所有响应
for (auto& future : futures) {
    auto result = future.get();
    // 处理结果...
}
```

## 兼容性

- **向后兼容**：保留了原有的同步接口 `CallMethod`
- **自动检测**：根据是否有 IOManager 自动选择异步或同步模式
- **渐进迁移**：可以逐步将同步调用迁移到异步调用

## 测试验证

提供了完整的测试用例：

1. **功能测试**：验证异步RPC的正确性
2. **性能测试**：对比同步和异步模式的性能差异
3. **并发测试**：验证高并发场景下的稳定性
4. **错误处理**：验证网络异常情况下的处理

## 总结

通过实现异步 RPC 客户端，我们实现了：

1. **真正的非阻塞**：发送请求后立即返回，不等待响应
2. **高并发支持**：可以同时处理多个请求
3. **事件驱动**：与 IOManager 深度集成，充分利用事件循环
4. **灵活的编程模型**：支持回调和 Future 两种风格
5. **显著的性能提升**：特别是在 Raft 算法的并发场景中

这个改进为 Raft-KV 系统提供了更高的性能和更好的可扩展性，是向着更通用、更高性能 RPC 框架发展的重要一步。
